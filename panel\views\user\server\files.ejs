<%- include('../../components/header', { title: 'Files' }) %>

<%
function getFileIcon(category) {
  const icons = {
    'Configuration Files': `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
      <path fill-rule="evenodd" d="M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm14.25 6a.75.75 0 0 1-.22.53l-2.25 2.25a.75.75 0 1 1-1.06-1.06L15.44 12l-1.72-1.72a.75.75 0 1 1 1.06-1.06l2.25 2.25c.141.14.22.331.22.53Zm-10.28-.53a.75.75 0 0 0 0 1.06l2.25 2.25a.75.75 0 1 0 1.06-1.06L8.56 12l1.72-1.72a.75.75 0 1 0-1.06-1.06l-2.25 2.25Z" clip-rule="evenodd" />
    </svg>
    `,
    'Documents': `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
      <path fill-rule="evenodd" d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625ZM7.5 15a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 7.5 15Zm.75 2.25a.75.75 0 0 0 0 1.5H12a.75.75 0 0 0 0-1.5H8.25Z" clip-rule="evenodd" />
      <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
    </svg>
    `,
    'Folder': `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
      <path d="M19.5 21a3 3 0 0 0 3-3v-4.5a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3V18a3 3 0 0 0 3 3h15ZM1.5 10.146V6a3 3 0 0 1 3-3h5.379a2.25 2.25 0 0 1 1.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 0 1 3 3v1.146A4.483 4.483 0 0 0 19.5 9h-15a4.483 4.483 0 0 0-3 1.146Z" />
    </svg>
    `,
    'Images': `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
      <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h16.5A2.25 2.25 0 0 1 22.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd" />
    </svg>
    `,
    'No Category': `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
      <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
      <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
    </svg>
    `,
  };

  return icons[category] || icons['No Category'];
}

// Function to check if a file is an image based on extension
function isImageFile(filename) {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff'];
  const extension = filename.split('.').pop().toLowerCase();
  return imageExtensions.includes(extension);
}
%>

<main class="h-screen m-auto">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <div class="w-60 h-full">
      <%- include('../../components/template') %>
    </div>

    <!-- Content -->
    <div class="flex-1 p-6 overflow-y-auto pt-16">
      <!-- Page Header -->
      <div class="sm:flex sm:items-center px-8 pt-4">
        <%- include('../../components/serverHeader') %>
        <% if (!(typeof serverStatus !== 'undefined' && serverStatus.daemonOffline && errorMessage && errorMessage.message)) { %>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex gap-2.5">
          <button id="createFile" onclick="openCreateFileModal()" type="button"
              class="border border-neutral-800/20 block rounded-xl bg-white hover:bg-neutral-200 dark:hover:bg-neutral-300 text-neutral-800 px-3 py-2 text-center text-sm font-medium shadow-lg transition duration-300 focus:outline focus:outline-2 focus:outline-offset-2 mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-4 inline-flex mr-1 text-neutral-800 mb-0.5">
                <path fill-rule="evenodd" d="M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875ZM12.75 12a.75.75 0 0 0-1.5 0v2.25H9a.75.75 0 0 0 0 1.5h2.25V18a.75.75 0 0 0 1.5 0v-2.25H15a.75.75 0 0 0 0-1.5h-2.25V12Z" clip-rule="evenodd" />
                <path d="M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z" />
              </svg>
              Create File
          </button>

          <button id="uploadFile" onclick="openUploadFileModal()" type="button"
              class="border border-neutral-800/20 block rounded-xl bg-white hover:bg-neutral-200 dark:hover:bg-neutral-300 text-neutral-800 px-3 py-2 text-center text-sm font-medium shadow-lg transition duration-300 focus:outline focus:outline-2 focus:outline-offset-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-4 inline-flex mr-1 text-neutral-800 mb-0.5">
                <path fill-rule="evenodd" d="M11.47 2.47a.75.75 0 0 1 1.06 0l4.5 4.5a.75.75 0 0 1-1.06 1.06l-3.22-3.22V16.5a.75.75 0 0 1-1.5 0V4.81L8.03 8.03a.75.75 0 0 1-1.06-1.06l4.5-4.5ZM3 15.75a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
              </svg>
              Upload File
          </button>
      </div>
        <% } %>
      </div>

      <%- include('../../components/installHeader') %>

      <!-- Daemon down message removed as requested -->

      <!-- Server Template -->
      <%- include('../../components/serverTemplate') %>

      <!-- Daemon Offline Warning -->
      <% if (typeof serverStatus !== 'undefined' && serverStatus.daemonOffline && errorMessage && errorMessage.message) { %>
      <div class="mx-8 mt-4 bg-red-500/10 border border-red-500/20 rounded-xl p-4 flex items-center">
        <div class="flex-shrink-0 mr-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
        <div>
          <h3 class="text-sm font-medium text-red-500">Connection Error</h3>
          <p class="text-xs text-red-400"><%= errorMessage.message %></p>
          <button onclick="window.location.reload()" class="mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-lg transition-colors">
            Retry Connection
          </button>
        </div>
      </div>
      <% } %>

      <div class="flex-grow px-8 mt-8 -mb-4">
        <h1 class="text-white text-sm">
          <a class="text-neutral-300 hover:text-neutral-200 transition" href="./files">/app/data/</a>
          <%
              if (req.query.path) {
                  const parts = req.query.path.split('/');
                  let currentPath = '';

                  parts.forEach((part, index) => {
                      currentPath += part;

                      if (index < parts.length - 1) {
                          %>
                          <a href="./files?path=<%= currentPath %>" class="text-neutral-300 hover:text-neutral-200 transition"><%= part %></a>/
                          <%
                          currentPath += '/';
                      } else {
                          %>
                          <span class="text-neutral-100"><%= part %></span>
                          <%
                      }
                  });
              }
          %>
        </h1>
       </div>

      <!-- File Table or Daemon Offline Message -->
      <% if (typeof serverStatus !== 'undefined' && serverStatus.daemonOffline && errorMessage && errorMessage.message) { %>
        <!-- Daemon Offline Message -->
        <div class="px-8 mt-8">
          <div class="bg-neutral-800/30 rounded-xl p-8 text-center">
            <div class="flex justify-center mb-6">
              <div class="w-16 h-16 rounded-full bg-neutral-700/50 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="m3 3 8.735 8.735m0 0a.374.374 0 1 1 .53.53m-.53-.53.53.53m0 0L21 21M14.652 9.348a3.75 3.75 0 0 1 0 5.304m2.121-7.425a6.75 6.75 0 0 1 0 9.546m2.121-11.667c3.808 3.807 3.808 9.98 0 13.788m-9.546-4.242a3.733 3.733 0 0 1-1.06-2.122m-1.061 4.243a6.75 6.75 0 0 1-1.625-6.929m-.496 9.05c-3.068-3.067-3.664-7.67-1.79-11.334M12 12h.008v.008H12V12Z" />
              </svg>
              </div>
            </div>
            <h2 class="text-xl font-semibold text-neutral-300">Daemon Offline</h2>
            <p class="mt-2 text-sm text-neutral-500 max-w-md mx-auto">
              The daemon appears to be offline. File management is unavailable until the connection is restored.
            </p>
            <div class="mt-6">
              <button onclick="window.location.reload()" class="px-4 py-2 bg-neutral-600 hover:bg-neutral-700 text-white text-sm rounded-lg transition-colors">
                Retry Connection
              </button>
            </div>
          </div>
        </div>
      <% } else { %>
        <!-- File Table -->
        <div class="px-8 mt-8">
          <div class="overflow-hidden rounded-lg shadow-md border border-neutral-700/10 dark:bg-neutral-400/20">
            <table class="min-w-full bg-white dark:bg-neutral-800">
            <thead class="text-neutral-800 dark:text-neutral-100">
              <tr>
                <th class="px-6 py-3 text-left">
                  <input type="checkbox" id="selectAll" class="form-checkbox h-5 w-5 text-neutral-500 bg-white/10 border border-white/15 rounded focus:ring-offset-neutral-800 focus:ring-neutral-700 transition focus:border-none file-checkbox">
                </th>
                <th class="px-6 py-3 text-left text-sm font-semibold">File Name</th>
                <th class="px-6 py-3 text-left text-sm font-semibold">Size</th>
                <th class="px-6 py-3 text-left text-sm font-semibold">&nbsp;</th>
              </tr>
            </thead>
            <tbody>
              <% files.forEach(file => { %>
                <tr class="hover:bg-neutral-50 dark:hover:bg-neutral-700/30 cursor-pointer">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white border-none">
                    <input type="checkbox" class="form-checkbox h-5 w-5 text-neutral-500 bg-white/5 border border-white/10 rounded focus:ring-offset-neutral-800 focus:ring-neutral-700 transition focus:border-none file-checkbox" data-filename="<%= currentPath && currentPath !== '/' ? currentPath.replace(/^\/+/, '') + '/' + file.name : file.name %>">
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white border-none">
                    <%
                      const isImage = file.type !== 'directory' && isImageFile(file.name);
                      const filePath = currentPath && currentPath !== '/'
                        ? currentPath.replace(/^\/+/, '') + '/' + file.name
                        : file.name;
                      const fileHref = file.type === 'directory'
                        ? `/server/${server.UUID}/files?path=${encodeURIComponent(filePath)}`
                        : isImage
                          ? '#'
                          : `/server/${server.UUID}/files/edit/${encodeURIComponent(filePath)}`;
                      const fileSize = (() => {
                        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                        let size = file.size;
                        let unitIndex = 0;
                        while (size >= 1024 && unitIndex < sizes.length - 1) {
                          size /= 1024;
                          unitIndex++;
                        }
                        return `${size.toFixed(2)} ${sizes[unitIndex]}`;
                      })();
                    %>
                    <a href="<%= fileHref %>" class="flex items-center" <% if (isImage) { %>onclick="event.preventDefault(); showImageViewer('<%= file.name %>', '<%= filePath %>', '<%= fileSize %>', '<%= server.UUID %>');"<% } %>>
                    <span class="mr-4">
                      <%- getFileIcon(
                          file.type === 'directory' ? 'Folder' : file.category || 'No Category'
                        ) %>
                    </span>
                    <%= file.name %>
                    </a>
                  </td>
                  <td class="px-6 py-4 text-sm text-neutral-700 dark:text-neutral-300">
                    <%= fileSize %>
                  </td>
                  <td class="px-6 py-4 text-sm">
                    <button onclick="toggleDropdown(event, '<%= file.name %>')" class="text-neutral-400 hover:text-white transition">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                      </svg>
                    </button>

                    <div id="dropdown-<%= file.name %>" class="absolute right-0 mt-2 w-fit mr-20 rounded-xl shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10 transform opacity-0 scale-95 transition-all duration-200 ease-out pointer-events-none">
                      <div
                        class="p-2 bg-gray-900 dark:bg-white rounded-lg shadow-md"
                        role="menu"
                        aria-orientation="vertical"
                        aria-labelledby="options-menu"
                      >
                        <button
                          class="w-full transition-colors duration-200 rounded-lg block px-4 py-1 text-sm font-medium text-neutral-800 dark:text-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-200 text-left"
                          role="menuitem"
                          onclick="rename('<%= file.name %>', '<%= currentPath && currentPath !== '/' ? currentPath.replace(/^\/+/, '') + '/' + file.name : file.name %>')"
                        >
                          Rename
                        </button>

                        <% if (file.type !== 'directory') { %>
                          <button
                            class="w-full transition-colors duration-200 rounded-lg block px-4 py-1 text-sm font-medium text-neutral-800 dark:text-neutral-900 hover:bg-red-50 dark:hover:bg-neutral-200 text-left"
                            role="menuitem"
                            onclick="downloadfile('<%= file.name %>', '<%= currentPath && currentPath !== '/' ? currentPath.replace(/^\/+/, '') + '/' + file.name : file.name %>')"
                          >
                            Download
                          </button>
                        <% } %>

                        <% if (file.name.toLowerCase().endsWith('.zip')) { %>
                        <button
                            class="w-full transition-colors duration-200 rounded-lg block px-4 py-1 text-sm font-medium text-neutral-800 dark:text-neutral-900 hover:bg-green-50 dark:hover:bg-neutral-200 text-left"
                            role="menuitem"
                            onclick="extractZip('<%= file.name %>', '<%= currentPath && currentPath !== '/' ? currentPath.replace(/^\/+/, '') + '/' + file.name : file.name %>')"
                        >
                            Extract Here
                        </button>
                        <% } %>

                        <button
                          class="w-full transition-colors duration-200 rounded-lg block px-4 py-1 text-sm font-medium text-red-500 dark:text-red-600 hover:bg-red-50 dark:hover:bg-red-50 text-left"
                          role="menuitem"
                          onclick="deletefile('<%= file.name %>', '<%= currentPath && currentPath !== '/' ? currentPath.replace(/^\/+/, '') + '/' + file.name : file.name %>')"
                        >
                          Delete
                        </button>
                      </div>
                    </div>

                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
      <% } %>
    </div>

    <!-- Modals and stuff -->

   <!-- Floating Action Bar (only show when daemon is online) -->
   <% if (!(typeof serverStatus !== 'undefined' && serverStatus.daemonOffline && errorMessage && errorMessage.message)) { %>
   <div id="floatingActionBar" class="fixed bottom-0 w-full bg-neutral-800 z-50 backdrop-blur text-white py-4 px-6 transform transition-transform duration-300 ease-in-out flex justify-between items-center">
    <span id="selectedFilesCount">0 files selected</span>
    <div class="flex gap-4">
        <button id="massDeleteBtn" class="w-full md:w-auto rounded-xl bg-red-600 hover:bg-red-500 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">
            Delete Selected
        </button>
        <button id="massArchiveBtn" class="w-full md:w-auto rounded-xl bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">
            Archive Selected
        </button>
    </div>
</div>
   <% } %>
 <!-- Mass Delete Confirmation Modal -->
 <div id="massDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300">
    <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
       <h2 class="text-2xl font-medium mb-1 text-neutral-800">Confirm Mass Deletion</h2>
       <p id="massDeleteMessage" class="mb-6 text-neutral-600"></p>
       <div class="flex justify-end space-x-4">
          <button onclick="closeMassDeleteModal()" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
          <button id="confirmMassDelete" class="w-full md:w-auto rounded-xl bg-red-600 hover:bg-red-500 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Delete</button>
       </div>
    </div>
 </div>
  <!-- Create file -->
  <div id="createFileModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300">
    <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
        <h2 class="text-2xl font-medium mb-1 text-neutral-800">Create File</h2>
        <p class="mb-6 text-neutral-600">Please choose a name for this file.</p>
        <input type="text"
        id="FileName"
        class="w-full px-3 py-2 border border-neutral-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:border-neutral-500 transition mb-4 text-neutral-800"
        placeholder="Enter file name"
        onkeydown="handleFileNameKeyPress(event)">
        <div class="flex justify-end space-x-4">
          <button onclick="closeCreateFileModal()" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
          <button onclick="confirmCreateFile()" class="w-full md:w-auto rounded-xl bg-neutral-950 hover:bg-neutral-800 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Create</button>
        </div>
    </div>
  </div>

  <!-- Upload file -->
  <div id="uploadFileModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300">
    <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
        <h2 class="text-2xl font-medium mb-1 text-neutral-800">Upload File</h2>
        <p class="mb-6 text-neutral-600">Select a file to upload to the current directory.</p>
        <div class="mb-4">
          <div class="mb-4">
            <label for="fileInput" class="w-full px-3 py-6 border-2 border-dashed border-neutral-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:border-neutral-500 transition text-neutral-800 text-center cursor-pointer hover:bg-neutral-50 block" id="dropZone">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-10 h-10 mx-auto mb-2 text-neutral-400">
                <path fill-rule="evenodd" d="M11.47 2.47a.75.75 0 0 1 1.06 0l4.5 4.5a.75.75 0 0 1-1.06 1.06l-3.22-3.22V16.5a.75.75 0 0 1-1.5 0V4.81L8.03 8.03a.75.75 0 0 1-1.06-1.06l4.5-4.5ZM3 15.75a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
              </svg>
              <p class="text-neutral-500">Drag & drop a file here or click to browse</p>
            </label>
            <input type="file" id="fileInput" class="hidden" onchange="handleFileInputChange(event)">
          </div>
          <div id="filePreview" class="hidden">
            <div class="flex items-center p-3 bg-neutral-100 rounded-xl">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 mr-3 text-neutral-600">
                <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
              </svg>
              <div class="flex-1 truncate">
                <p id="selectedFileName" class="font-medium text-neutral-800 truncate"></p>
                <p id="selectedFileSize" class="text-sm text-neutral-500"></p>
              </div>
              <button onclick="removeSelectedFile()" class="ml-2 text-neutral-400 hover:text-neutral-600">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                  <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div class="flex justify-end space-x-4">
          <button onclick="closeUploadFileModal()" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
          <button id="uploadButton" class="w-full md:w-auto rounded-xl bg-neutral-950 hover:bg-neutral-800 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2" disabled>Upload</button>
        </div>
    </div>
  </div>

  </div>
</main>

<%- include('../../components/toast')%>
<%- include('../../components/loadingPopup')%>
<%- include('../../components/imageViewer')%>

<!-- Rename Modal -->
<div id="renameModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300">
  <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
    <h2 class="text-2xl font-medium mb-1 text-neutral-800">Rename File</h2>
    <p class="mb-6 text-neutral-600">Please enter a new name for this file.</p>
    <input type="text" id="newFileName" class="w-full px-3 py-2 border border-neutral-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:border-neutral-500 transition mb-4 text-neutral-800" placeholder="Enter new name">
    <input type="hidden" id="currentFileName">
    <input type="hidden" id="currentFilePath">
    <div class="flex justify-end space-x-4">
      <button onclick="closeRenameModal()" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
      <button onclick="confirmRename()" class="w-full md:w-auto rounded-xl bg-neutral-950 hover:bg-neutral-800 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Rename</button>
    </div>
  </div>
</div>

<script>
  function handleFileNameKeyPress(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        confirmCreateFile();
    }
  }

  document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.showImageViewer !== 'function' && typeof window.imageViewerSystem === 'function') {
      const { showImageViewer, closeImageViewer, downloadViewedImage } = window.imageViewerSystem();
      window.showImageViewer = showImageViewer;
      window.closeImageViewer = closeImageViewer;
      window.downloadViewedImage = downloadViewedImage;
    }

    document.addEventListener('contextmenu', (event) => {
      event.preventDefault();
      const target = event.target.closest('tr');

      if (target) {
        const fileName = target.querySelector('button').getAttribute('onclick').match(/'(.+?)'/)[1];
        showDropdown(event, fileName);
      }
    });

    document.addEventListener('click', () => {
      document.querySelectorAll('[id^="dropdown-"]').forEach((el) => closeDropdown(el));
    });
  });

  function showDropdown(event, fileName) {
    const dropdown = document.getElementById(`dropdown-${fileName}`);

    const { clientX: mouseX, clientY: mouseY } = event;
    const dropdownRect = dropdown.getBoundingClientRect();
    const bodyWidth = document.body.clientWidth;

    dropdown.style.left = `${Math.min(mouseX, bodyWidth - dropdownRect.width)}px`;
    dropdown.style.top = `${mouseY}px`;

    openDropdown(dropdown);
  }

  function openDropdown(dropdown) {
    dropdown.classList.remove('opacity-0', 'scale-95', 'pointer-events-none');
    dropdown.classList.add('opacity-100', 'scale-100');
  }

  function closeDropdown(dropdown) {
    dropdown.classList.remove('opacity-100', 'scale-100');
    dropdown.classList.add('opacity-0', 'scale-95', 'pointer-events-none');
  }

  function deletefile(fileName, filePath) {
    // Create confirmation modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300';
    modal.id = 'deleteFileModal';

    modal.innerHTML = `
      <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
        <h2 class="text-2xl font-medium mb-1 text-neutral-800">Confirm Deletion</h2>
        <p class="mb-6 text-neutral-600">Are you sure you want to delete <strong>${fileName}</strong>? This action cannot be undone.</p>
        <div class="flex justify-end space-x-4">
          <button id="cancelDeleteBtn" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
          <button id="confirmDeleteBtn" class="w-full md:w-auto rounded-xl bg-red-600 hover:bg-red-500 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Delete</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    setTimeout(() => {
      modal.classList.remove('opacity-0', 'pointer-events-none');
      modal.querySelector('div').classList.remove('scale-95');
      modal.querySelector('div').classList.add('scale-100');
    }, 10);

    document.getElementById('cancelDeleteBtn').addEventListener('click', () => {
      modal.classList.add('opacity-0', 'pointer-events-none');
      modal.querySelector('div').classList.remove('scale-100');
      modal.querySelector('div').classList.add('scale-95');
      setTimeout(() => document.body.removeChild(modal), 300);
    });

    document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
      modal.classList.add('opacity-0', 'pointer-events-none');
      modal.querySelector('div').classList.remove('scale-100');
      modal.querySelector('div').classList.add('scale-95');
      setTimeout(() => document.body.removeChild(modal), 300);

      const loader = showLoadingPopup('Deleting File', 'Removing file from server...');

      fetch('/server/<%= server.UUID %>/files/rm/' + encodeURIComponent(filePath), { method: 'DELETE' })
      .then(response => {
        loader.close();
        if (response.ok) {
          showToast(`File ${fileName} deleted successfully`, 'success');
          setTimeout(() => location.reload(), 1000);
        } else {
          showToast('Failed to delete file', 'error');
        }
      })
      .catch(error => {
        loader.close();
        console.error('Error:', error);
        showToast('An error occurred while deleting the file', 'error');
      });
    });
  }

  function downloadfile(fileName, filePath) {
    fetch('/server/<%= server.UUID %>/files/download/' + encodeURIComponent(filePath), { method: 'GET' })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error downloading file: ${response.statusText}`);
            }
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        })
        .catch(error => {
            console.error('Download failed:', error);
            showToast('Failed to download file', 'error');
        });
}

 // File Select thingy

  const selectAllCheckbox = document.getElementById('selectAll');
  const fileCheckboxes = document.querySelectorAll('.file-checkbox:not(#selectAll)');
  const floatingActionBar = document.getElementById('floatingActionBar');
  const selectedFilesCount = document.getElementById('selectedFilesCount');
  const massDeleteBtn = document.getElementById('massDeleteBtn');
  const massDeleteModal = document.getElementById('massDeleteModal');
  const massDeleteMessage = document.getElementById('massDeleteMessage');
  const confirmMassDeleteBtn = document.getElementById('confirmMassDelete');

  let selectedFiles = [];

  function updateSelectedFiles() {
    const selectedCount = selectedFiles.length;
    if (selectedFiles.length > 0) {
    floatingActionBar.classList.remove('translate-y-full');
  } else {
    floatingActionBar.classList.add('translate-y-full');
  }
    document.getElementById('selectedFilesCount').innerText = `${selectedCount} file${selectedCount !== 1 ? 's' : ''} selected`;

    document.getElementById('massDeleteBtn').disabled = selectedCount === 0;
    document.getElementById('massArchiveBtn').disabled = selectedCount === 0;
}

document.getElementById('massArchiveBtn').addEventListener('click', () => {
    if (selectedFiles.length > 0) {
        // Create archive confirmation modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300';
        modal.id = 'archiveConfirmModal';

        modal.innerHTML = `
          <div class="bg-white rounded-xl p-8 max-w-md w-full transform scale-95 transition-transform duration-300">
            <h2 class="text-2xl font-medium mb-1 text-neutral-800">Confirm Archive</h2>
            <p class="mb-6 text-neutral-600">Are you sure you want to archive ${selectedFiles.length} selected file${selectedFiles.length !== 1 ? 's' : ''}?</p>
            <div class="flex justify-end space-x-4">
              <button id="cancelArchiveBtn" class="px-5 py-2 bg-neutral-200 text-neutral-800 rounded-xl hover:bg-neutral-300 transition">Cancel</button>
              <button id="confirmArchiveBtn" class="w-full md:w-auto rounded-xl bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">Archive</button>
            </div>
          </div>
        `;

        document.body.appendChild(modal);

        setTimeout(() => {
          modal.classList.remove('opacity-0', 'pointer-events-none');
          modal.querySelector('div').classList.remove('scale-95');
          modal.querySelector('div').classList.add('scale-100');
        }, 10);

        document.getElementById('cancelArchiveBtn').addEventListener('click', () => {
          modal.classList.add('opacity-0', 'pointer-events-none');
          modal.querySelector('div').classList.remove('scale-100');
          modal.querySelector('div').classList.add('scale-95');
          setTimeout(() => document.body.removeChild(modal), 300);
        });

        document.getElementById('confirmArchiveBtn').addEventListener('click', () => {
          modal.classList.add('opacity-0', 'pointer-events-none');
          modal.querySelector('div').classList.remove('scale-100');
          modal.querySelector('div').classList.add('scale-95');
          setTimeout(() => document.body.removeChild(modal), 300);

          archiveFiles(selectedFiles);
        });
    }
});

function initializeSelectedFiles() {
  const storedSelectedFiles = JSON.parse(sessionStorage.getItem('selectedFiles') || '[]');
  fileCheckboxes.forEach(checkbox => {
    checkbox.checked = storedSelectedFiles.includes(checkbox.dataset.filename);
  });
  updateSelectedFiles();
}

// Select All functionality
selectAllCheckbox.addEventListener('change', event => {
    const isChecked = event.target.checked;

    // Get all file checkboxes (excluding the select all checkbox itself)
    const fileCheckboxes = document.querySelectorAll('.file-checkbox:not(#selectAll)');

    fileCheckboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
        const fileName = checkbox.dataset.filename;

        if (isChecked) {
            if (!selectedFiles.includes(fileName)) {
                selectedFiles.push(fileName);
            }
        } else {
            selectedFiles = selectedFiles.filter(name => name !== fileName);
        }
    });

    updateSelectedFiles();
});

document.querySelectorAll('.file-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', event => {
        const fileName = event.target.dataset.filename;

        if (event.target.checked) {
            selectedFiles.push(fileName);
        } else {
            selectedFiles = selectedFiles.filter(name => name !== fileName);
        }

        // Update select all checkbox state
        const fileCheckboxes = document.querySelectorAll('.file-checkbox:not(#selectAll)');
        const checkedCount = document.querySelectorAll('.file-checkbox:not(#selectAll):checked').length;
        selectAllCheckbox.checked = checkedCount === fileCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < fileCheckboxes.length;

        updateSelectedFiles();
    });
});

function archiveFiles(files) {
    const loader = showLoadingPopup('Creating Archive', 'Preparing files for archiving...');
    loader.updateProgress(20, 'Compressing files...');

    fetch('/server/<%= server.UUID %>/zip', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          relativePath: files,
          zipname: 'archive'
         })
    })
    .then(response => response.json())
    .then(data => {
        loader.updateProgress(100, 'Archive created!');
        setTimeout(() => {
            loader.close();
            if (data.success) {
                showToast('Files archived successfully!', 'success');
                selectedFiles = [];
                updateSelectedFiles();
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Failed to archive files', 'error');
            }
        }, 500);
    })
    .catch(error => {
        loader.close();
        console.error('Error:', error);
        showToast('An error occurred while creating the archive', 'error');
    });
}

massDeleteBtn.addEventListener('click', function() {
  massDeleteMessage.textContent = `Are you sure you want to delete ${selectedFiles.length} file${selectedFiles.length !== 1 ? 's' : ''}? This is a permanent action and cannot be reversed.`;
  massDeleteModal.classList.remove('opacity-0', 'pointer-events-none');
  massDeleteModal.querySelector('div').classList.remove('scale-95');
  massDeleteModal.querySelector('div').classList.add('scale-100');
});

function closeMassDeleteModal() {
  massDeleteModal.classList.add('opacity-0', 'pointer-events-none');
  massDeleteModal.querySelector('div').classList.remove('scale-100');
  massDeleteModal.querySelector('div').classList.add('scale-95');
}

confirmMassDeleteBtn.addEventListener('click', async function() {
  closeMassDeleteModal();

  const loader = showLoadingPopup('Deleting Files', `Removing ${selectedFiles.length} files...`);
  loader.updateProgress(10, 'Processing deletion requests...');

  const deletePromises = selectedFiles.map(fileName =>
    fetch('/server/<%= server.UUID %>/files/rm/' + encodeURIComponent(fileName), { method: 'DELETE' })
  );

  try {
    await Promise.all(deletePromises);
    loader.updateProgress(100, 'Files deleted successfully!');
    setTimeout(() => {
      loader.close();
      showToast(`${selectedFiles.length} files deleted successfully`, 'success');
      setTimeout(() => window.location.reload(), 1000);
    }, 500);
  } catch (error) {
    loader.close();
    console.error('Error deleting files:', error);
    showToast('Failed to delete files', 'error');
  }
});

document.addEventListener('DOMContentLoaded', initializeSelectedFiles);

window.addEventListener('beforeunload', () => {
  sessionStorage.removeItem('selectedFiles');
});

updateSelectedFiles();

// New File
function openCreateFileModal() {
    const modal = document.getElementById('createFileModal');
    const input = document.getElementById('FileName');

    modal.classList.remove('opacity-0', 'pointer-events-none');
    modal.querySelector('div').classList.remove('scale-95');
    modal.querySelector('div').classList.add('scale-100');

    input.focus();
  }

  function closeCreateFileModal() {
    const modal = document.getElementById('createFileModal');
    modal.classList.add('opacity-0', 'pointer-events-none');
    modal.querySelector('div').classList.remove('scale-100');
    modal.querySelector('div').classList.add('scale-95');
  }

  async function confirmCreateFile() {
    const FileName = document.getElementById('FileName').value.trim();
    if (FileName) {
      closeCreateFileModal();
      const loader = showLoadingPopup('Creating File', 'Creating new file...');

      try {
        const encodedFileName = encodeURIComponent(FileName);
        const createFile = await fetch(`/server/<%= server.UUID %>/files/<%= currentPath %>/${encodedFileName}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ content: '' }),
        });

        if (createFile.ok) {
          loader.updateProgress(100, 'File created successfully!');
          setTimeout(() => {
            loader.close();
            showToast(`File ${FileName} created successfully`, 'success');
            setTimeout(() => location.reload(), 1000);
          }, 500);
        } else {
          loader.close();
          showToast('Failed to create file', 'error');
        }
      } catch (error) {
        loader.close();
        console.error('Error creating file:', error);
        showToast('An error occurred while creating the file', 'error');
      }
    } else {
      closeCreateFileModal();
    }
  }

  function openRenameModal(fileName, filePath) {
    const modal = document.getElementById('renameModal');
    const input = document.getElementById('newFileName');
    document.getElementById('currentFileName').value = fileName;
    document.getElementById('currentFilePath').value = filePath;

    input.value = fileName;

    modal.classList.remove('opacity-0', 'pointer-events-none');
    modal.querySelector('div').classList.remove('scale-95');
    modal.querySelector('div').classList.add('scale-100');

    // Select the filename part without the extension
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex > 0) {
      input.setSelectionRange(0, lastDotIndex);
    } else {
      input.select();
    }
    input.focus();
  }

  function closeRenameModal() {
    const modal = document.getElementById('renameModal');
    modal.classList.add('opacity-0', 'pointer-events-none');
    modal.querySelector('div').classList.remove('scale-100');
    modal.querySelector('div').classList.add('scale-95');
  }

  function confirmRename() {
    const newName = document.getElementById('newFileName').value.trim();
    const fileName = document.getElementById('currentFileName').value;
    const filePath = document.getElementById('currentFilePath').value;

    if (newName && newName !== fileName) {
      closeRenameModal();
      const loader = showLoadingPopup('Renaming File', 'Processing rename request...');

      fetch(`/server/<%= server.UUID %>/rename`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          path: filePath,
          newName: newName
        })
      })
      .then(response => {
        loader.close();
        if (response.ok) {
          showToast(`File renamed to ${newName} successfully`, 'success');
          setTimeout(() => location.reload(), 1000);
        } else {
          showToast('Failed to rename file', 'error');
        }
      })
      .catch(error => {
        loader.close();
        console.error('Renaming failed:', error);
        showToast('Failed to rename file', 'error');
      });
    } else {
      closeRenameModal();
    }
  }

  // Add event listener for Enter key in rename modal
  document.getElementById('newFileName').addEventListener('keydown', function(event) {
    if (event.key === 'Enter') {
      event.preventDefault();
      confirmRename();
    }
  });

  function rename(fileName, filePath) {
    openRenameModal(fileName, filePath);
  }

  async function extractZip(fileName, filePath) {
    try {
        const loader = showLoadingPopup('Extracting Archive', 'Preparing to extract files...');
        loader.updateProgress(20, 'Extracting files...');

        const parentPath = filePath.substring(0, filePath.lastIndexOf('/'));

        const response = await fetch(`/server/<%= server.UUID %>/unzip`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                relativePath: parentPath || '/',
                zipname: fileName
            })
        });

        if (response.ok) {
            loader.updateProgress(100, 'Files extracted successfully!');
            setTimeout(() => {
                loader.close();
                showToast('File extracted successfully', 'success');
                setTimeout(() => location.reload(), 1000);
            }, 500);
        } else {
            loader.close();
            const error = await response.json();
            showToast(error.error || 'Failed to extract file', 'error');
        }
    } catch (error) {
        console.error('Error extracting file:', error);
        showToast('Failed to extract file', 'error');
    }
  }

  // File Upload Functions
  // Use window object to ensure the variable is globally accessible
  window.selectedFile = null;

  function openUploadFileModal() {
    const modal = document.getElementById('uploadFileModal');
    if (!modal) {
      console.error('Upload modal not found');
      return;
    }

    modal.classList.remove('opacity-0', 'pointer-events-none');
    const modalContent = modal.querySelector('div');
    if (modalContent) {
      modalContent.classList.remove('scale-95');
      modalContent.classList.add('scale-100');
    }

    // Reset the file selection
    removeSelectedFile();

    // Reset the file input
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
      fileInput.value = '';
    }
  }

  function closeUploadFileModal() {
    const modal = document.getElementById('uploadFileModal');
    if (!modal) {
      console.error('Upload modal not found');
      return;
    }

    modal.classList.add('opacity-0', 'pointer-events-none');
    const modalContent = modal.querySelector('div');
    if (modalContent) {
      modalContent.classList.remove('scale-100');
      modalContent.classList.add('scale-95');
    }
  }

  function removeSelectedFile() {
    window.selectedFile = null;

    const filePreview = document.getElementById('filePreview');
    const dropZone = document.getElementById('dropZone');
    const uploadButton = document.getElementById('uploadButton');

    if (filePreview) {
      filePreview.classList.add('hidden');
    }

    if (dropZone) {
      dropZone.classList.remove('hidden');
    }

    if (uploadButton) {
      uploadButton.disabled = true;
    }
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));

    return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function handleFileSelection(file) {
    if (!file) {
      console.error('No file provided to handleFileSelection');
      return;
    }

    // Make sure file is a valid File object
    if (!(file instanceof File)) {
      console.error('Invalid file object:', file);
      showToast('Invalid file selected', 'error');
      return;
    }

    console.log('Setting selected file:', file.name, file.size, file.type);
    window.selectedFile = file;

    const fileNameElement = document.getElementById('selectedFileName');
    const fileSizeElement = document.getElementById('selectedFileSize');
    const filePreviewElement = document.getElementById('filePreview');
    const dropZoneElement = document.getElementById('dropZone');
    const uploadButtonElement = document.getElementById('uploadButton');

    if (fileNameElement) {
      fileNameElement.textContent = file.name;
    }

    if (fileSizeElement) {
      fileSizeElement.textContent = formatFileSize(file.size);
    }

    if (filePreviewElement) {
      filePreviewElement.classList.remove('hidden');
    }

    if (dropZoneElement) {
      dropZoneElement.classList.add('hidden');
    }

    if (uploadButtonElement) {
      uploadButtonElement.disabled = false;
    }
  }

  async function confirmFileUpload() {
    console.log('confirmFileUpload called, selectedFile:', window.selectedFile);

    if (!window.selectedFile) {
      showToast('Please select a file to upload', 'error');
      return;
    }

    if (!(window.selectedFile instanceof File)) {
      console.error('Selected file is not a valid File object:', window.selectedFile);
      showToast('Invalid file selected', 'error');
      return;
    }

    // Store file info before closing modal
    const file = window.selectedFile;
    const fileName = file.name;
    const fileSize = file.size;

    closeUploadFileModal();
    const loader = showLoadingPopup('Uploading File', 'Preparing to upload...');

    try {
      // Check if file size is too large (limit to 100MB)
      const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
      if (fileSize > MAX_FILE_SIZE) {
        loader.close();
        showToast('File is too large. Maximum size is 100MB', 'error');
        return;
      }

      // Use FormData instead of JSON for more reliable file uploads
      const formData = new FormData();
      formData.append('file', file);
      formData.append('path', '<%= currentPath %>');
      formData.append('fileName', fileName);

      loader.updateProgress(30, 'Uploading file...');

      try {
        // Use XMLHttpRequest for better progress tracking and reliability
        const xhr = new XMLHttpRequest();

        xhr.open('POST', `/server/<%= server.UUID %>/upload`, true);

        xhr.upload.onprogress = function(e) {
          if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            loader.updateProgress(30 + (percentComplete * 0.6), `Uploading: ${percentComplete}%`);
          }
        };

        xhr.onload = function() {
          if (xhr.status >= 200 && xhr.status < 300) {
            loader.updateProgress(100, 'File uploaded successfully!');
            setTimeout(() => {
              loader.close();
              showToast(`File ${fileName} uploaded successfully`, 'success');
              setTimeout(() => location.reload(), 1000);
            }, 500);
          } else {
            loader.close();
            try {
              const errorData = JSON.parse(xhr.responseText);
              console.error('Upload error response:', errorData);

              let errorMessage = 'Failed to upload file';
              if (errorData.error) {
                errorMessage = errorData.error;
              } else if (errorData.details && errorData.details.error) {
                errorMessage = errorData.details.error;
              }

              showToast(errorMessage, 'error');
            } catch (e) {
              console.error('Error parsing error response:', e);
              showToast(`Failed to upload file: ${xhr.status} ${xhr.statusText}`, 'error');
            }
          }
        };

        xhr.onerror = function() {
          loader.close();
          console.error('XHR error during upload');
          showToast('Connection error during file upload', 'error');
        };

        xhr.ontimeout = function() {
          loader.close();
          console.error('XHR timeout during upload');
          showToast('Upload timed out. Please try again with a smaller file', 'error');
        };

        // Set a longer timeout for large files
        xhr.timeout = 300000; // 5 minutes

        console.log('Sending file upload request:', fileName, fileSize);
        xhr.send(formData);
      } catch (error) {
        loader.close();
        console.error('Error uploading file:', error);
        showToast('An error occurred while uploading the file', 'error');
      }
    } catch (error) {
      loader.close();
      console.error('Error uploading file:', error);
      showToast('An error occurred while uploading the file', 'error');
    }
  }

  // Global variable for selected file
  window.selectedFile = null;

  // Direct handler for file input change
  function handleFileInputChange(event) {
    console.log('File input change event triggered directly');
    if (event.target && event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      console.log('File selected via input:', file.name, file.size, file.type);
      handleFileSelection(file);
    } else {
      console.log('No file selected in file input change event');
    }
  }

  // Initialize file upload functionality when the DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing file upload functionality');

    // Get DOM elements
    const dropZone = document.getElementById('dropZone');
    const uploadButton = document.getElementById('uploadButton');

    if (!dropZone) {
      console.error('Drop zone element not found in the DOM');
      return;
    }

    // Handle drag and drop
    dropZone.addEventListener('dragover', function(event) {
      event.preventDefault();
      dropZone.classList.add('bg-neutral-50', 'border-neutral-400');
    });

    dropZone.addEventListener('dragleave', function(event) {
      event.preventDefault();
      dropZone.classList.remove('bg-neutral-50', 'border-neutral-400');
    });

    dropZone.addEventListener('drop', function(event) {
      event.preventDefault();
      dropZone.classList.remove('bg-neutral-50', 'border-neutral-400');

      if (event.dataTransfer && event.dataTransfer.files && event.dataTransfer.files.length > 0) {
        const file = event.dataTransfer.files[0];
        console.log('File dropped:', file.name, file.size, file.type);
        handleFileSelection(file);
      } else {
        console.log('No valid files in drop event');
      }
    });

    // Add direct click handler for upload button
    if (uploadButton) {
      uploadButton.addEventListener('click', function(event) {
        event.preventDefault();
        console.log('Upload button clicked');
        confirmFileUpload();
      });
    }

    // Initialize by clearing any previously selected file
    removeSelectedFile();
    console.log('File upload functionality initialized');
  });
</script>

<%- include('../../components/footer') %>