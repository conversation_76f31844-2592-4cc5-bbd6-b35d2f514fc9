/**
 * ╳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╳
 *      AirLink - Open Source Project by AirlinkLabs
 *      Repository: https://github.com/airlinklabs/daemon
 *
 *     © 2024 AirlinkLabs. Licensed under the MIT License
 * ╳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╳
 */

import fs from 'fs';
import path from 'path';
import config from './config';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  gray: '\x1b[90m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m',
};

const logLevels = {
  error: {
    color: colors.red,
    bgColor: colors.bgRed,
    icon: '✖',
    label: 'ERROR',
  },
  warn: {
    color: colors.yellow,
    bgColor: colors.bgYellow,
    icon: '⚠',
    label: 'WARN',
  },
  info: {
    color: colors.blue,
    bgColor: colors.bgBlue,
    icon: 'ℹ',
    label: 'INFO',
  },
  success: {
    color: colors.green,
    bgColor: colors.bgGreen,
    icon: '✔',
    label: 'SUCCESS',
  },
  debug: {
    color: colors.magenta,
    bgColor: colors.bgMagenta,
    icon: '⚙',
    label: 'DEBUG',
  },
};

const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

class Logger {
  private originalConsoleLog: (...args: any[]) => void;
  private originalConsoleError: (...args: any[]) => void;
  private originalConsoleWarn: (...args: any[]) => void;
  private originalConsoleInfo: (...args: any[]) => void;

  constructor() {
    this.originalConsoleLog = console.log;
    this.originalConsoleError = console.error;
    this.originalConsoleWarn = console.warn;
    this.originalConsoleInfo = console.info;

    // Override console methods to use our logger
    console.log = (...args: any[]) => this.log(...args);
    console.error = (...args: any[]) => this.error('Error', args.join(' '));
    console.warn = (...args: any[]) => this.warn(args.join(' '));
    console.info = (...args: any[]) => this.info(args.join(' '));
  }

  private getTimestamp(): string {
    const now = new Date();
    return now.toISOString().replace('T', ' ').split('.')[0];
  }

  private async writeToFile(filePath: string, data: string): Promise<void> {
    fs.appendFile(filePath, data, (err) => {
      if (err) {
        this.originalConsoleLog(`Failed to write to log file: ${err}`);
      }
    });
  }
  
  private formatMessage(
    level: keyof typeof logLevels,
    message: string,
  ): string {
    const { color, bgColor, icon, label } = logLevels[level];
    const timestamp = this.getTimestamp();
    const consoleOutput = `${colors.gray}${timestamp}${colors.reset} ${color}${icon} ${bgColor}${colors.bright}${label}${colors.reset} ${color}${message}${colors.reset}`;
    const fileOutput = `[${timestamp}] ${label}: ${message}\n`;
    const logFile = path.join(
      logsDir,
      level === 'error' ? 'error.log' : 'combined.log',
    );
    
    this.writeToFile(logFile, fileOutput);

    return consoleOutput;
  }

  error(message: string, error: unknown): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const formattedMessage = this.formatMessage(
      'error',
      `${message}: ${errorMessage}`,
    );
    this.originalConsoleLog(formattedMessage);
  }

  warn(message: any): void {
    const formattedMessage = this.formatMessage('warn', String(message));
    this.originalConsoleLog(formattedMessage);
  }

  info(message: any): void {
    const formattedMessage = this.formatMessage('info', String(message));
    this.originalConsoleLog(formattedMessage);
  }

  success(message: any): void {
    const formattedMessage = this.formatMessage('success', String(message));
    this.originalConsoleLog(formattedMessage);
  }

  debug(...args: unknown[]): void {
    if (config.DEBUG) {
      const message = args
        .map((arg) =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg),
        )
        .join(' ');
      const formattedMessage = this.formatMessage('debug', message);
      this.originalConsoleLog(formattedMessage);
    }
  }

  log(...args: any[]): void {
    const message = args
      .map((arg) =>
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg),
      )
      .join(' ');
    this.info(message);
  }
}

const logger = new Logger();

export default logger;
