{"name": "daemon", "version": "1.0.0", "main": "app.js", "scripts": {"build": "tsc", "start": "node dist/app/app.js", "clean": "node scripts/clean.js", "start:dev": "npm run build && npm run start", "dev": "nodemon --exec ts-node src/app/app.ts", "configure": "ts-node src/handlers/configure.ts"}, "keywords": [], "author": "", "license": "MIT", "description": "", "dependencies": {"@types/tar": "^6.1.13", "@types/uuid": "^10.0.0", "archiver": "^7.0.1", "axios": "^1.7.9", "body-parser": "^2.2.0", "chalk": "^4.1.2", "compression": "^1.7.5", "dockerode": "^4.0.2", "dotenv": "^16.4.5", "express": "^4.21.2", "express-basic-auth": "^1.2.1", "express-ws": "^5.0.2", "glob": "^11.0.2", "minecraft-status": "^1.1.0", "os-utils": "^0.0.14", "tar": "^7.4.3", "tsx": "^4.19.2"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/compression": "^1.7.5", "@types/dockerode": "^3.3.32", "@types/express": "^5.0.0", "@types/express-ws": "^3.0.5", "@types/glob": "^8.1.0", "@types/node": "^22.10.2", "@types/os-utils": "^0.0.4", "@types/ws": "^8.5.13", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.2", "ws": "^8.18.0"}}